<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title_header">{{type=='week'?"我的周考核":"我的月考核"}}</text>
	
	    <view class="nav-btn"  @click="historyExam">
			<text class="right_title">{{type=='week'?"历史周考核":"历史月考核"}}</text>
		</view>
	</view>
	<image
	   class="background-image" 
	   src="/static/image/mine_bj.png"
	   mode="cover"
	   
	 />
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="exam-card">
			<view class="card-header">
				<text class="exam-type">当前考核</text>
				<text class="yinying"></text>
			</view>

			<view class="exam-info">
				<view class="info-row">
					
					<text class="iconfont label_icon">&#xe645;</text>
					<text class="info-label">考核名称：</text>
					<text class="info-value">2025年1月份第一周考核</text>
				</view>

				<view class="info-row">
					
					<text class="iconfont score_icon">&#xe61c;</text>
					<text class="info-label">考核得分：</text>
					<text class="info-value score-value">100</text>
				</view>
			</view>

			<view class="card-footer">
				<view class="detail-btn" @click="viewDetail">
					<text class="btn-text">查看详情</text>
				</view>
			</view>
		</view>
		<view class="ranking-card">
			<view class="ranking-header">
				<text class="iconfont ranking-icon">&#xe61d;</text>
				<text class="ranking-title">排行榜</text>
			</view>

			<view class="my-ranking">
				<view class="ranking_left">
					<text class="my-rank-label">我的排名：</text>
					<text class="my-rank-value">06</text>
				</view>
				<view class="ranking_right">
					<text class="my-score-label">我的得分：</text>
					<text class="my-score-value">152</text>
				</view>
				
				
			</view>

			<view class="ranking-list">
				<view class="ranking-header-row">
					<text class="header-rank">排名</text>
					<text class="header-name">姓名</text>
					<text class="header-score">得分</text>
				</view>

				<view class="ranking-item" v-for="(item,index) in 10" :key="index">
					<view class="rank-badge">
						<image class="paiming_img" v-if="index<3" :src="'/static/image/di_'+(index+1)+'.png'" mode="aspectFit"></image>
						<text v-else class="rank-text">{{index+1}}</text>
					</view>
					<view class="user-avatar">
						<text class="avatar-text"></text>
					</view>
					<text class="user-name">张某某</text>
					<text class="user-score">203</text>
				</view>

				
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	
	const type=ref<string>('')
	
	
	// 历史考核
	const historyExam=()=>{
		if(type.value=='week'){
			console.log('历史周考核');
		}else{
			console.log('历史月考核');
		}
	}

	// 查看详情
	const viewDetail=()=>{
		if(type.value=='week'){
			console.log('查看周考核详情');
			uni.navigateTo({
				url:'/pages/home/<USER>/examDetails?type='+type.value
			})
		}else{
			console.log('查看月考核详情');
			uni.navigateTo({
				url:'/pages/home/<USER>/examDetails?type='+type.value
			})
		}
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['type']
		if (id != null) {
		  type.value = id as string
		}
	})
</script>

<style lang="scss" scoped>
.page-container{
	background-color: #ffffff;
}
.nav-bar {
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;

  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */

}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title_header {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  // font-weight: bold;
  color: #fff; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // max-width: 60%;
}
.right_title{
	font-size: 28rpx;
	color: #fff;
}
.icon_font{
	font-size: 32rpx;
	 color: #fff; /* 黑色文字 */
}

.background-image {
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: -1;
}

/* 考核卡片样式 */
.exam-card {
  margin: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  
  .card-header {
    margin-bottom: 30rpx;
	position: relative;
	.yinying{
		position: absolute;
		width: 100rpx;
		height: 13rpx;
		background: #1385F6;
		bottom: 0rpx;
		opacity: 0.19;
	}
  }
  
  .exam-type {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  
  .info-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: 24rpx;
    .label_icon{
  	  font-size: 30rpx;
  	  color: #fb8898;
  	  margin-right: 10rpx;
    }
    .score_icon{
  	  font-size: 28rpx;
  	  color: #2db9ed;
  	  margin-right: 10rpx;
    }
  }
  
  .info-label {
    font-size: 28rpx;
    color: #131313;
    margin-right: 10rpx;
  }
  
  .info-value {
    font-size: 28rpx;
    color: #131313;
    font-weight: 400;
  }
  
  .score-value {
    color: #4CAF50;
    font-size: 32rpx;
    font-weight: bold;
  }
  
  .card-footer {
    align-items: flex-end;
  }
  
  .detail-btn {
    background: #007AFF;
    border-radius: 25rpx;
    padding: 16rpx 40rpx;
  }
  
  .btn-text {
    color: #fff;
    font-size: 28rpx;
    line-height: 1;
  }
}

/* 排行榜卡片样式 */
.ranking-card {
  margin: 20rpx 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.ranking-header {
  flex-direction: row;
  align-items: center;
  margin-bottom: 30rpx;

  .ranking-icon {
    font-size: 40rpx;
    color: #007AFF;
    margin-right: 10rpx;
  }

  .ranking-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.my-ranking {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background: #FFF8E1;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  .ranking_left{
	  flex:1;
	  flex-direction: row;
	  align-items: center;
  }
  .ranking_right{
  	  // flex:1;
  	  flex-direction: row;
  	  align-items: center;
  }

  .my-rank-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 10rpx;
  }

  .my-rank-value {
    font-size: 32rpx;
    font-weight: bold;
    color: #FF9800;
    margin-right: 40rpx;
  }

  .my-score-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 10rpx;
  }

  .my-score-value {
    font-size: 32rpx;
    font-weight: bold;
    color: #FF9800;
  }
}

.ranking-header-row {
  flex-direction: row;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 10rpx;

  .header-rank {
	width: 15%;
    // width: 80rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #131313;
    text-align: center;
    margin-right: 40rpx;
  }

  .header-name {
    flex: 1;
    font-size: 28rpx;
    font-weight: bold;
    color: #131313;
    margin-left: 20rpx;
    margin-right: 20rpx;
  }

  .header-score {
    font-size: 28rpx;
    font-weight: bold;
    color: #131313;
    text-align: center;
  }
}

.ranking-item {
  flex-direction: row;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F5;

  &:last-child {
    border-bottom: none;
  }
}

.rank-badge {
  width: 15%;
  // width: 60rpx;
  // height: 60rpx;
  justify-content: center;
  align-items: center;
  margin-right: 40rpx;

  .paiming_img {
    width: 50rpx;
    height: 50rpx;
  }
  .rank-text {
  	 width: 60rpx;
  	height: 60rpx;
  	border-radius: 30rpx;
  	background: #E3F2FD;
    font-size: 28rpx;
  	text-align: center;
  	line-height: 60rpx;
    font-weight: bold;
    color: #2196F3;
  }
}


.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: #E0E0E0;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  margin-left: 20rpx;

  .avatar-text {
    font-size: 28rpx;
    color: #666;
    font-weight: bold;
  }
}

.user-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  margin-right: 20rpx;
}

.user-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #4CAF50;
}


</style>
