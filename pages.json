{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://doc.dcloud.net.cn/uni-app-x/collocation/pagesjson.html
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/index",
			"style": {
				"navigationBarTitleText": "个人中心",
				"navigationStyle": "custom"
			}
		},
		
		{
			"path": "pages/representative/index",
			"style": {
				"navigationBarTitleText": "待办",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/editPassword",
			"style": {
				"navigationBarTitleText": "修改密码"
			}
		},
		
		{
			"path": "pages/login/register",
			"style": {
				"navigationBarTitleText": "注册"
			}
		},
		{
			"path": "pages/mine/entryDetails",
			"style": {
				"navigationBarTitleText": "入职详情"
			}
		},
		{
			"path": "pages/mine/mineInfo",
			"style": {
				"navigationBarTitleText": "个人信息"
			}
		},
		{
			"path": "pages/task/assign",
			"style": {
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/task/records",
			"style": {
				"navigationBarTitleText": "下发记录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/common/dept-post-user-select",
			"style": {
				"navigationBarTitleText": "选择指派人员",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/task/checklist-select",
			"style": {
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/mine/zhengshu/mineZhengshu",
			"style" : 
			{
				"navigationBarTitleText" : "我的证书",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/mine/zhengshu/zhengshuDetails",
			"style" : 
			{
				"navigationBarTitleText" : "证书详情",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/mine/pingfenjilu/weekRate",
			"style" : 
			{
				"navigationBarTitleText" : "周评价纪录",
				"navigationStyle": "custom" 
				
			}
		},
		{
			"path" : "pages/mine/pingfenjilu/weekRateDetails",
			"style" : 
			{
				"navigationBarTitleText" : "周考核评分记录详情",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/representative/examine/weekExamineDetails",
			"style" : 
			{
				"navigationBarTitleText" : "周考核详情",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/representative/approve/rzApprove",
			"style" : 
			{
				"navigationBarTitleText" : "入职审批",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/representative/approve/ygtzApprove",
			"style" : 
			{
				"navigationBarTitleText" : "员工调整",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/mine/zhuanban/transferRecord",
			"style" : 
			{
				"navigationBarTitleText" : "转办纪录",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/mine/zhuanban/details",
			"style" : 
			{
				"navigationBarTitleText" : "转办详情",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/home/<USER>/examIndex",
			"style" : 
			{
				"navigationBarTitleText" : "考核",
				"navigationStyle": "custom" 
			}
		},
		{
			"path" : "pages/home/<USER>/examDetails",
			"style" : 
			{
				"navigationBarTitleText" : "考核详情"
			}
		}
		
		
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app x",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#131313",
		"selectedColor": "#0E82F6",
		"borderStyle": "#97A5BC",
		"backgroundColor": "#ffffff",
		// #ifdef H5
		"height": "106rpx",
		// #endif
		"list": [
			{
				"pagePath": "pages/home/<USER>", // 页面路径
				"text": "首页", // 名称
				"iconPath": "static/image/home.png", // 默认图标
				"selectedIconPath": "static/image/home_active.png" // 选中图标
			},
			{
				"pagePath": "pages/representative/index",
				"text": "待办",
				"iconPath": "static/image/representative.png",
				"selectedIconPath": "static/image/representative_active.png"
			},
			{
				"pagePath": "pages/mine/index",
				"text": "我的",
				"iconPath": "static/image/mine.png",
				"selectedIconPath": "static/image/mine_active.png"
			}
		]
	},
	"uniIdRouter": {}
}